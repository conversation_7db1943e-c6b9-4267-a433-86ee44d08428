"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/app/admin/kyc/page.tsx":
/*!************************************!*\
  !*** ./src/app/admin/kyc/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KYCManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n/* harmony import */ var _components_kyc_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/kyc/KYCStatusBadge */ \"(app-pages-browser)/./src/components/kyc/KYCStatusBadge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction KYCManagementPage() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submissions, setSubmissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [totalSubmissions, setTotalSubmissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSubmission, setSelectedSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showReviewModal, setShowReviewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reviewLoading, setReviewLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentUrls, setDocumentUrls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [expandedSubmission, setExpandedSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const itemsPerPage = 10;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"Current user:\", user);\n        console.log(\"User role:\", user === null || user === void 0 ? void 0 : user.role);\n        fetchSubmissions();\n    }, [\n        currentPage,\n        searchTerm,\n        statusFilter,\n        user\n    ]);\n    const fetchSubmissions = async ()=>{\n        try {\n            setLoading(true);\n            const result = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getAllKYCSubmissions(currentPage, itemsPerPage, statusFilter || undefined, searchTerm || undefined);\n            setSubmissions(result.submissions);\n            setTotalSubmissions(result.total);\n        } catch (error) {\n            console.error(\"Error fetching KYC submissions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleViewDocuments = async (submission)=>{\n        try {\n            setSelectedSubmission(submission);\n            setShowReviewModal(true);\n            console.log(\"Loading documents for submission:\", submission.id);\n            console.log(\"Document paths:\", {\n                id_front: submission.id_document_front_url,\n                id_back: submission.id_document_back_url,\n                selfie: submission.selfie_photo_url,\n                address_proof: submission.address_proof_url\n            });\n            // Get signed URLs for documents\n            const urls = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getKYCDocumentUrls(submission);\n            console.log(\"Generated signed URLs:\", urls);\n            setDocumentUrls(urls);\n        } catch (error) {\n            console.error(\"Error loading documents:\", error);\n            // Still show the modal even if documents fail to load\n            setDocumentUrls({});\n        }\n    };\n    const handleReviewSubmission = async (status, reason, notes)=>{\n        if (!selectedSubmission || !user) return;\n        try {\n            setReviewLoading(true);\n            await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.reviewKYCSubmission(selectedSubmission.id, {\n                status,\n                rejection_reason: reason,\n                admin_notes: notes\n            }, user.id);\n            // Refresh submissions\n            await fetchSubmissions();\n            setShowReviewModal(false);\n            setSelectedSubmission(null);\n            setDocumentUrls({});\n        } catch (error) {\n            console.error(\"Error reviewing submission:\", error);\n        } finally{\n            setReviewLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n            case \"under_review\":\n                return \"text-blue-600 bg-blue-50 border-blue-200\";\n            case \"approved\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            default:\n                return \"text-gray-600 bg-gray-50 border-gray-200\";\n        }\n    };\n    const totalPages = Math.ceil(totalSubmissions / itemsPerPage);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-amber-100 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-6 w-6 text-amber-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"KYC Management\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Review and manage user identity verification submissions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white px-4 py-2 rounded-lg border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Total Submissions: \"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: totalSubmissions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-lg border border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search by name, email, or ID number...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"All Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"pending\",\n                                            children: \"Pending\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"under_review\",\n                                            children: \"Under Review\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"approved\",\n                                            children: \"Approved\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rejected\",\n                                            children: \"Rejected\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-amber-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, this) : submissions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No KYC Submissions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"No submissions found matching your criteria.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: submissions.map((submission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                            lineNumber: 222,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 221,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: submission.full_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                lineNumber: 225,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: submission.user.email\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_kyc_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                status: submission.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 235,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: submission.id_document_type.replace(\"_\", \" \").toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(submission.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: submission.address\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setExpandedSubmission(expandedSubmission === submission.id ? null : submission.id),\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                                        children: expandedSubmission === submission.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewDocuments(submission),\n                                                        className: \"flex items-center gap-2 px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Review\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.AnimatePresence, {\n                                        children: expandedSubmission === submission.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"ID Number:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-gray-600\",\n                                                                children: submission.id_document_number || \"Not provided\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Date of Birth:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-gray-600\",\n                                                                children: submission.date_of_birth ? new Date(submission.date_of_birth).toLocaleDateString() : \"Not provided\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    submission.submission_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Notes:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-gray-600\",\n                                                                children: submission.submission_notes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    submission.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-red-700\",\n                                                                children: \"Rejection Reason:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-red-600\",\n                                                                children: submission.rejection_reason\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, submission.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between bg-white px-6 py-3 rounded-lg border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                (currentPage - 1) * itemsPerPage + 1,\n                                \" to \",\n                                Math.min(currentPage * itemsPerPage, totalSubmissions),\n                                \" of \",\n                                totalSubmissions,\n                                \" results\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm bg-amber-100 text-amber-800 rounded\",\n                                    children: [\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.AnimatePresence, {\n                    children: showReviewModal && selectedSubmission && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n                        onClick: ()=>{\n                            setShowReviewModal(false);\n                            setSelectedSubmission(null);\n                            setDocumentUrls({});\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                scale: 0.9,\n                                opacity: 0\n                            },\n                            animate: {\n                                scale: 1,\n                                opacity: 1\n                            },\n                            exit: {\n                                scale: 0.9,\n                                opacity: 0\n                            },\n                            className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Review KYC Submission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mt-1\",\n                                                        children: [\n                                                            selectedSubmission.full_name,\n                                                            \" - \",\n                                                            selectedSubmission.user.email\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowReviewModal(false);\n                                                    setSelectedSubmission(null);\n                                                    setDocumentUrls({});\n                                                },\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Full Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission.full_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"ID Document Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission.id_document_type.replace(\"_\", \" \").toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"ID Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission.id_document_number || \"Not provided\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission.date_of_birth ? new Date(selectedSubmission.date_of_birth).toLocaleDateString() : \"Not provided\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission.address\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this),\n                                                selectedSubmission.submission_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"User Notes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission.submission_notes\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                    children: \"Submitted Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: Object.entries(documentUrls).map((param)=>{\n                                                        let [key, url] = param;\n                                                        const docType = key === \"id_front\" ? \"ID Front\" : key === \"id_back\" ? \"ID Back\" : key === \"selfie\" ? \"Selfie\" : key === \"address_proof\" ? \"Address Proof\" : \"Document\";\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: docType\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"aspect-video bg-gray-100 rounded-lg overflow-hidden\",\n                                                                    children: url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: url,\n                                                                        alt: docType,\n                                                                        className: \"w-full h-full object-contain\",\n                                                                        onError: (e)=>{\n                                                                            const target = e.target;\n                                                                            target.style.display = \"none\";\n                                                                            const parent = target.parentElement;\n                                                                            if (parent) {\n                                                                                parent.innerHTML = '\\n                                        <div class=\"flex items-center justify-center h-full\">\\n                                          <div class=\"text-center\">\\n                                            <div class=\"h-8 w-8 text-gray-400 mx-auto mb-2\">⚠️</div>\\n                                            <p class=\"text-sm text-gray-600\">Failed to load document</p>\\n                                          </div>\\n                                        </div>\\n                                      ';\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center h-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"h-8 w-8 text-gray-400 mx-auto mb-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                    lineNumber: 472,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"No document available\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                    lineNumber: 473,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                            lineNumber: 471,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: url,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"inline-flex items-center gap-2 mt-2 text-sm text-amber-600 hover:text-amber-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"View Full Size\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 27\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-end gap-3 pt-6 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleReviewSubmission(\"rejected\", \"Documents do not meet requirements\"),\n                                                    disabled: reviewLoading,\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors\",\n                                                    children: [\n                                                        reviewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Reject\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleReviewSubmission(\"approved\"),\n                                                    disabled: reviewLoading,\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors\",\n                                                    children: [\n                                                        reviewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Approve\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCManagementPage, \"6oDjz/2KH7qB8zl4tWx3rirYY4U=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = KYCManagementPage;\nvar _c;\n$RefreshReg$(_c, \"KYCManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/kyc/page.tsx\n"));

/***/ })

});