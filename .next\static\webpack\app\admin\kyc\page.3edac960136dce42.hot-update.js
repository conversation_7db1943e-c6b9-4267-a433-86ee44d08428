"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/lib/services/kycStorage.ts":
/*!****************************************!*\
  !*** ./src/lib/services/kycStorage.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCStorageService: function() { return /* binding */ KYCStorageService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass KYCStorageService {\n    /**\n   * Ensure KYC bucket exists before operations\n   */ static async ensureBucketExists() {\n        try {\n            // Check if bucket exists\n            const { data: buckets, error: listError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.listBuckets();\n            if (listError) {\n                console.warn(\"Cannot list buckets (likely due to RLS policies):\", listError.message);\n                console.log(\"Assuming KYC bucket exists and continuing...\");\n                return;\n            }\n            const bucketExists = buckets === null || buckets === void 0 ? void 0 : buckets.some((bucket)=>bucket.name === this.BUCKET_NAME);\n            if (!bucketExists) {\n                console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' not found in bucket list\"));\n                console.log(\"This is expected if RLS policies restrict bucket listing\");\n                console.log(\"Assuming bucket exists and continuing with upload...\");\n                return;\n            }\n            console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' confirmed to exist\"));\n        } catch (error) {\n            console.warn(\"Error checking KYC bucket existence:\", error);\n            console.log(\"Assuming bucket exists and continuing with operations...\");\n        }\n    }\n    /**\n   * Validate KYC document file\n   */ static validateFile(file, documentType) {\n        // Check file size\n        if (file.size > this.MAX_FILE_SIZE) {\n            throw new Error(\"File size must be less than \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB\"));\n        }\n        // Check file type\n        if (!this.ALLOWED_TYPES.includes(file.type)) {\n            throw new Error(\"File type \".concat(file.type, \" is not supported. Allowed types: \").concat(this.ALLOWED_TYPES.join(\", \")));\n        }\n        // Additional validation for specific document types\n        if (documentType === \"selfie\" && file.type === \"application/pdf\") {\n            throw new Error(\"Selfie photos must be image files, not PDF\");\n        }\n        // Check if file name is reasonable\n        if (file.name.length > 255) {\n            throw new Error(\"File name is too long\");\n        }\n    }\n    /**\n   * Generate secure filename for KYC document\n   */ static generateSecureFileName(userId, documentType, originalName) {\n        var _originalName_split_pop;\n        const timestamp = Date.now();\n        const randomId = Math.random().toString(36).substring(2);\n        const fileExt = ((_originalName_split_pop = originalName.split(\".\").pop()) === null || _originalName_split_pop === void 0 ? void 0 : _originalName_split_pop.toLowerCase()) || \"jpg\";\n        // Create folder structure: userId/documentType/timestamp-randomId.ext\n        return \"\".concat(userId, \"/\").concat(documentType, \"/\").concat(timestamp, \"-\").concat(randomId, \".\").concat(fileExt);\n    }\n    /**\n   * Upload a single KYC document\n   */ static async uploadKYCDocument(file, userId, documentType) {\n        try {\n            // Check authentication first\n            const { data: { user }, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (authError || !user) {\n                console.error(\"Authentication error during KYC upload:\", authError);\n                throw new Error(\"Authentication required. Please log in and try again.\");\n            }\n            if (user.id !== userId) {\n                console.error(\"User ID mismatch during KYC upload:\", {\n                    authUserId: user.id,\n                    providedUserId: userId\n                });\n                throw new Error(\"Authentication error. Please log out and log in again.\");\n            }\n            console.log(\"Authenticated user \".concat(user.id, \" uploading KYC document: \").concat(documentType));\n            // Ensure bucket exists (or assume it exists)\n            await this.ensureBucketExists();\n            // Validate file\n            this.validateFile(file, documentType);\n            // Generate secure filename\n            const fileName = this.generateSecureFileName(userId, documentType, file.name);\n            console.log(\"Uploading KYC document: \".concat(fileName, \" to bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).upload(fileName, file, {\n                cacheControl: \"3600\",\n                upsert: true // Allow overwriting for resubmissions\n            });\n            if (error) {\n                console.error(\"KYC document upload error:\", {\n                    error,\n                    fileName,\n                    fileSize: file.size,\n                    fileType: file.type,\n                    userId,\n                    documentType\n                });\n                // Provide more specific error messages\n                if (error.message.includes(\"row-level security\") || error.message.includes(\"new row violates row-level security\")) {\n                    throw new Error(\"Upload failed: Storage access denied. Please ensure you are logged in and try again.\");\n                } else if (error.message.includes(\"Bucket not found\")) {\n                    throw new Error(\"Upload failed: Storage bucket not configured. Please contact support.\");\n                } else if (error.message.includes(\"File size\") || error.message.includes(\"too large\")) {\n                    throw new Error(\"Upload failed: File too large. Maximum size is \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB.\"));\n                } else if (error.message.includes(\"Invalid file type\")) {\n                    throw new Error(\"Upload failed: Invalid file type. Allowed types: \".concat(this.ALLOWED_TYPES.join(\", \")));\n                } else {\n                    throw new Error(\"Upload failed: \".concat(error.message));\n                }\n            }\n            console.log(\"KYC document upload successful:\", {\n                fileName,\n                path: data === null || data === void 0 ? void 0 : data.path,\n                fullPath: data === null || data === void 0 ? void 0 : data.fullPath\n            });\n            // For private buckets, we need to create signed URLs for access\n            // Return the file path for now, signed URL will be generated when needed\n            return fileName;\n        } catch (error) {\n            console.error(\"Error uploading KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upload multiple KYC documents\n   */ static async uploadKYCDocuments(documents, userId) {\n        const results = {};\n        try {\n            // Upload all documents concurrently\n            const uploadPromises = documents.map(async (doc)=>{\n                const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type);\n                return {\n                    type: doc.type,\n                    filePath\n                };\n            });\n            const uploadResults = await Promise.all(uploadPromises);\n            // Build results object\n            uploadResults.forEach((result)=>{\n                results[result.type] = result.filePath;\n            });\n            return results;\n        } catch (error) {\n            console.error(\"Error uploading multiple KYC documents:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get signed URL for KYC document (for admin review)\n   */ static async getKYCDocumentSignedUrl(filePath) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            console.log(\"Creating signed URL for file: \".concat(filePath, \" in bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).createSignedUrl(filePath, expiresIn);\n            if (error) {\n                console.error(\"Error creating signed URL:\", {\n                    error,\n                    filePath,\n                    bucketName: this.BUCKET_NAME,\n                    expiresIn\n                });\n                throw new Error(\"Failed to create signed URL for \".concat(filePath, \": \").concat(error.message));\n            }\n            if (!(data === null || data === void 0 ? void 0 : data.signedUrl)) {\n                throw new Error(\"No signed URL returned for \".concat(filePath));\n            }\n            console.log(\"Successfully created signed URL for: \".concat(filePath));\n            return data.signedUrl;\n        } catch (error) {\n            console.error(\"Error getting KYC document signed URL:\", {\n                error,\n                filePath,\n                bucketName: this.BUCKET_NAME\n            });\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC document (for cleanup or resubmission)\n   */ static async deleteKYCDocument(filePath) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).remove([\n                filePath\n            ]);\n            if (error) {\n                console.error(\"Error deleting KYC document:\", error);\n                throw new Error(\"Failed to delete document: \".concat(error.message));\n            }\n            console.log(\"KYC document deleted successfully:\", filePath);\n        } catch (error) {\n            console.error(\"Error deleting KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get multiple signed URLs for KYC documents\n   */ static async getKYCDocumentSignedUrls(filePaths) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            console.log(\"Creating signed URLs for \".concat(filePaths.length, \" files:\"), filePaths);\n            const urlPromises = filePaths.map(async (filePath)=>{\n                try {\n                    const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn);\n                    return {\n                        filePath,\n                        signedUrl,\n                        success: true\n                    };\n                } catch (error) {\n                    console.error(\"Failed to create signed URL for \".concat(filePath, \":\"), error);\n                    return {\n                        filePath,\n                        signedUrl: \"\",\n                        success: false,\n                        error\n                    };\n                }\n            });\n            const results = await Promise.all(urlPromises);\n            const urlMap = {};\n            const failedFiles = [];\n            results.forEach((result)=>{\n                if (result.success && result.signedUrl) {\n                    urlMap[result.filePath] = result.signedUrl;\n                } else {\n                    failedFiles.push(result.filePath);\n                }\n            });\n            if (failedFiles.length > 0) {\n                console.warn(\"Failed to create signed URLs for files:\", failedFiles);\n            }\n            console.log(\"Successfully created \".concat(Object.keys(urlMap).length, \" signed URLs out of \").concat(filePaths.length, \" requested\"));\n            return urlMap;\n        } catch (error) {\n            console.error(\"Error getting multiple KYC document signed URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Validate document type enum\n   */ static isValidDocumentType(type) {\n        return [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ].includes(type);\n    }\n    /**\n   * Get human-readable document type name\n   */ static getDocumentTypeName(type) {\n        const names = {\n            id_front: \"ID Document (Front)\",\n            id_back: \"ID Document (Back)\",\n            selfie: \"Selfie Photo\",\n            address_proof: \"Address Proof\"\n        };\n        return names[type];\n    }\n    /**\n   * Get document type requirements\n   */ static getDocumentTypeRequirements(type) {\n        const requirements = {\n            id_front: \"Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)\",\n            id_back: \"Clear photo of the back side of your government-issued ID\",\n            selfie: \"Clear selfie photo holding your ID document next to your face\",\n            address_proof: \"Recent utility bill, bank statement, or official document showing your address (PDF or image)\"\n        };\n        return requirements[type];\n    }\n}\nKYCStorageService.BUCKET_NAME = \"kyc-documents\";\nKYCStorageService.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents\n;\nKYCStorageService.ALLOWED_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/webp\",\n    \"application/pdf\" // Allow PDF for address proof documents\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kycStorage.ts\n"));

/***/ })

});