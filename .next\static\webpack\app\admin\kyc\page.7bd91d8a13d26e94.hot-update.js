"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/kyc/page",{

/***/ "(app-pages-browser)/./src/app/admin/kyc/page.tsx":
/*!************************************!*\
  !*** ./src/app/admin/kyc/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ KYCManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,ChevronDown,ChevronUp,CreditCard,Download,Eye,FileText,Loader2,MapPin,Search,Shield,User,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/kyc */ \"(app-pages-browser)/./src/lib/services/kyc.ts\");\n/* harmony import */ var _components_kyc_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/kyc/KYCStatusBadge */ \"(app-pages-browser)/./src/components/kyc/KYCStatusBadge.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction KYCManagementPage() {\n    var _selectedSubmission_user, _selectedSubmission_id_document_type;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submissions, setSubmissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [totalSubmissions, setTotalSubmissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSubmission, setSelectedSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showReviewModal, setShowReviewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reviewLoading, setReviewLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentUrls, setDocumentUrls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [expandedSubmission, setExpandedSubmission] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const itemsPerPage = 10;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"Current user:\", user);\n        console.log(\"User role:\", user === null || user === void 0 ? void 0 : user.role);\n        fetchSubmissions();\n    }, [\n        currentPage,\n        searchTerm,\n        statusFilter,\n        user\n    ]);\n    const fetchSubmissions = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"Fetching KYC submissions with params:\", {\n                currentPage,\n                itemsPerPage,\n                statusFilter,\n                searchTerm\n            });\n            const result = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getAllKYCSubmissions(currentPage, itemsPerPage, statusFilter || undefined, searchTerm || undefined);\n            console.log(\"Fetched submissions:\", result);\n            setSubmissions(result.submissions);\n            setTotalSubmissions(result.total);\n        } catch (error) {\n            console.error(\"Error fetching KYC submissions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleViewDocuments = async (submission)=>{\n        try {\n            setSelectedSubmission(submission);\n            setShowReviewModal(true);\n            console.log(\"Loading documents for submission:\", submission.id);\n            console.log(\"Document paths:\", {\n                id_front: submission.id_document_front_url,\n                id_back: submission.id_document_back_url,\n                selfie: submission.selfie_photo_url,\n                address_proof: submission.address_proof_url\n            });\n            // Get signed URLs for documents\n            const urls = await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.getKYCDocumentUrls(submission);\n            console.log(\"Generated signed URLs:\", urls);\n            setDocumentUrls(urls);\n        } catch (error) {\n            console.error(\"Error loading documents:\", error);\n            // Still show the modal even if documents fail to load\n            setDocumentUrls({});\n        }\n    };\n    const handleReviewSubmission = async (status, reason, notes)=>{\n        if (!selectedSubmission || !user) return;\n        try {\n            setReviewLoading(true);\n            await _lib_services_kyc__WEBPACK_IMPORTED_MODULE_3__.KYCService.reviewKYCSubmission(selectedSubmission.id, {\n                status,\n                rejection_reason: reason,\n                admin_notes: notes\n            }, user.id);\n            // Refresh submissions\n            await fetchSubmissions();\n            setShowReviewModal(false);\n            setSelectedSubmission(null);\n            setDocumentUrls({});\n        } catch (error) {\n            console.error(\"Error reviewing submission:\", error);\n        } finally{\n            setReviewLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"text-yellow-600 bg-yellow-50 border-yellow-200\";\n            case \"under_review\":\n                return \"text-blue-600 bg-blue-50 border-blue-200\";\n            case \"approved\":\n                return \"text-green-600 bg-green-50 border-green-200\";\n            case \"rejected\":\n                return \"text-red-600 bg-red-50 border-red-200\";\n            default:\n                return \"text-gray-600 bg-gray-50 border-gray-200\";\n        }\n    };\n    const totalPages = Math.ceil(totalSubmissions / itemsPerPage);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-amber-100 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-6 w-6 text-amber-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"KYC Management\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Review and manage user identity verification submissions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white px-4 py-2 rounded-lg border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Total Submissions: \"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-gray-900\",\n                                        children: totalSubmissions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-lg border border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search by name, email, or ID number...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"All Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"pending\",\n                                            children: \"Pending\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"under_review\",\n                                            children: \"Under Review\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"approved\",\n                                            children: \"Approved\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"rejected\",\n                                            children: \"Rejected\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-8 w-8 animate-spin text-amber-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 13\n                    }, this) : submissions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: \"No KYC Submissions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"No submissions found matching your criteria.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: submissions.map((submission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                className: \"p-6 hover:bg-gray-50 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                            lineNumber: 231,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-gray-900\",\n                                                                                children: submission.full_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                lineNumber: 234,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-600\",\n                                                                                children: submission.user.email\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                lineNumber: 235,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_kyc_KYCStatusBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                status: submission.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: submission.id_document_type.replace(\"_\", \" \").toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 245,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: new Date(submission.created_at).toLocaleDateString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 252,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: submission.address\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 253,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setExpandedSubmission(expandedSubmission === submission.id ? null : submission.id),\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors\",\n                                                        children: expandedSubmission === submission.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewDocuments(submission),\n                                                        className: \"flex items-center gap-2 px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Review\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.AnimatePresence, {\n                                        children: expandedSubmission === submission.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: \"auto\"\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"ID Number:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-gray-600\",\n                                                                children: submission.id_document_number || \"Not provided\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Date of Birth:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 text-gray-600\",\n                                                                children: submission.date_of_birth ? new Date(submission.date_of_birth).toLocaleDateString() : \"Not provided\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    submission.submission_notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700\",\n                                                                children: \"Notes:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-gray-600\",\n                                                                children: submission.submission_notes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    submission.rejection_reason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-red-700\",\n                                                                children: \"Rejection Reason:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 31\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-red-600\",\n                                                                children: submission.rejection_reason\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, submission.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between bg-white px-6 py-3 rounded-lg border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                (currentPage - 1) * itemsPerPage + 1,\n                                \" to \",\n                                Math.min(currentPage * itemsPerPage, totalSubmissions),\n                                \" of \",\n                                totalSubmissions,\n                                \" results\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm bg-amber-100 text-amber-800 rounded\",\n                                    children: [\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.AnimatePresence, {\n                    children: showReviewModal && selectedSubmission && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4\",\n                        onClick: ()=>{\n                            setShowReviewModal(false);\n                            setSelectedSubmission(null);\n                            setDocumentUrls({});\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                scale: 0.9,\n                                opacity: 0\n                            },\n                            animate: {\n                                scale: 1,\n                                opacity: 1\n                            },\n                            exit: {\n                                scale: 0.9,\n                                opacity: 0\n                            },\n                            className: \"bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n                            onClick: (e)=>e.stopPropagation(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Review KYC Submission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mt-1\",\n                                                        children: [\n                                                            selectedSubmission === null || selectedSubmission === void 0 ? void 0 : selectedSubmission.full_name,\n                                                            \" - \",\n                                                            selectedSubmission === null || selectedSubmission === void 0 ? void 0 : (_selectedSubmission_user = selectedSubmission.user) === null || _selectedSubmission_user === void 0 ? void 0 : _selectedSubmission_user.email\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowReviewModal(false);\n                                                    setSelectedSubmission(null);\n                                                    setDocumentUrls({});\n                                                },\n                                                className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Full Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission === null || selectedSubmission === void 0 ? void 0 : selectedSubmission.full_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"ID Document Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission === null || selectedSubmission === void 0 ? void 0 : (_selectedSubmission_id_document_type = selectedSubmission.id_document_type) === null || _selectedSubmission_id_document_type === void 0 ? void 0 : _selectedSubmission_id_document_type.replace(\"_\", \" \").toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"ID Number\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: (selectedSubmission === null || selectedSubmission === void 0 ? void 0 : selectedSubmission.id_document_number) || \"Not provided\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Date of Birth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: (selectedSubmission === null || selectedSubmission === void 0 ? void 0 : selectedSubmission.date_of_birth) ? new Date(selectedSubmission.date_of_birth).toLocaleDateString() : \"Not provided\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission === null || selectedSubmission === void 0 ? void 0 : selectedSubmission.address\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (selectedSubmission === null || selectedSubmission === void 0 ? void 0 : selectedSubmission.submission_notes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"md:col-span-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                            children: \"User Notes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-900\",\n                                                            children: selectedSubmission.submission_notes\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                    children: \"Submitted Documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: Object.entries(documentUrls).map((param)=>{\n                                                        let [key, url] = param;\n                                                        const docType = key === \"id_front\" ? \"ID Front\" : key === \"id_back\" ? \"ID Back\" : key === \"selfie\" ? \"Selfie\" : key === \"address_proof\" ? \"Address Proof\" : \"Document\";\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: docType\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"aspect-video bg-gray-100 rounded-lg overflow-hidden\",\n                                                                    children: url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: url,\n                                                                        alt: docType,\n                                                                        className: \"w-full h-full object-contain\",\n                                                                        onError: (e)=>{\n                                                                            const target = e.target;\n                                                                            target.style.display = \"none\";\n                                                                            const parent = target.parentElement;\n                                                                            if (parent) {\n                                                                                parent.innerHTML = '\\n                                        <div class=\"flex items-center justify-center h-full\">\\n                                          <div class=\"text-center\">\\n                                            <div class=\"h-8 w-8 text-gray-400 mx-auto mb-2\">⚠️</div>\\n                                            <p class=\"text-sm text-gray-600\">Failed to load document</p>\\n                                          </div>\\n                                        </div>\\n                                      ';\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 33\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-center h-full\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"h-8 w-8 text-gray-400 mx-auto mb-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                    lineNumber: 481,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: \"No document available\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                                    lineNumber: 482,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                    href: url,\n                                                                    target: \"_blank\",\n                                                                    rel: \"noopener noreferrer\",\n                                                                    className: \"inline-flex items-center gap-2 mt-2 text-sm text-amber-600 hover:text-amber-700\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \"View Full Size\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, key, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 27\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-end gap-3 pt-6 border-t border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleReviewSubmission(\"rejected\", \"Documents do not meet requirements\"),\n                                                    disabled: reviewLoading,\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors\",\n                                                    children: [\n                                                        reviewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 512,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Reject\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleReviewSubmission(\"approved\"),\n                                                    disabled: reviewLoading,\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors\",\n                                                    children: [\n                                                        reviewLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_ChevronDown_ChevronUp_CreditCard_Download_Eye_FileText_Loader2_MapPin_Search_Shield_User_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Approve\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\kyc\\\\page.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(KYCManagementPage, \"6oDjz/2KH7qB8zl4tWx3rirYY4U=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = KYCManagementPage;\nvar _c;\n$RefreshReg$(_c, \"KYCManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/kyc/page.tsx\n"));

/***/ })

});