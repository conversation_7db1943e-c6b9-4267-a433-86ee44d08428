'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  Search,
  Filter,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  User,
  Calendar,
  MapPin,
  CreditCard,
  AlertTriangle,
  Download,
  Loader2,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import { KYCService, KYCSubmission } from '@/lib/services/kyc'
import { KYCStorageService } from '@/lib/services/kycStorage'
import KYCStatusBadge from '@/components/kyc/KYCStatusBadge'
import { useAuth } from '@/contexts/AuthContext'

interface KYCSubmissionWithUser extends KYCSubmission {
  user: {
    full_name: string
    email: string
  }
}

export default function KYCManagementPage() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(true)
  const [submissions, setSubmissions] = useState<KYCSubmissionWithUser[]>([])
  const [totalSubmissions, setTotalSubmissions] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [selectedSubmission, setSelectedSubmission] = useState<KYCSubmissionWithUser | null>(null)
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [reviewLoading, setReviewLoading] = useState(false)
  const [documentUrls, setDocumentUrls] = useState<Record<string, string>>({})
  const [expandedSubmission, setExpandedSubmission] = useState<string | null>(null)

  const itemsPerPage = 10

  useEffect(() => {
    console.log('Current user:', user)
    console.log('User role:', user?.role)
    fetchSubmissions()
  }, [currentPage, searchTerm, statusFilter, user])

  const fetchSubmissions = async () => {
    try {
      setLoading(true)
      console.log('Fetching KYC submissions with params:', {
        currentPage,
        itemsPerPage,
        statusFilter,
        searchTerm
      })

      const result = await KYCService.getAllKYCSubmissions(
        currentPage,
        itemsPerPage,
        statusFilter || undefined,
        searchTerm || undefined
      )

      console.log('Fetched submissions:', result)
      setSubmissions(result.submissions as KYCSubmissionWithUser[])
      setTotalSubmissions(result.total)
    } catch (error) {
      console.error('Error fetching KYC submissions:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleViewDocuments = async (submission: KYCSubmissionWithUser) => {
    try {
      setSelectedSubmission(submission)
      setShowReviewModal(true)

      console.log('Loading documents for submission:', submission.id)
      console.log('Document paths:', {
        id_front: submission.id_document_front_url,
        id_back: submission.id_document_back_url,
        selfie: submission.selfie_photo_url,
        address_proof: submission.address_proof_url
      })

      // Get signed URLs for documents
      const urls = await KYCService.getKYCDocumentUrls(submission)
      console.log('Generated signed URLs:', urls)
      setDocumentUrls(urls)
    } catch (error) {
      console.error('Error loading documents:', error)
      // Still show the modal even if documents fail to load
      setDocumentUrls({})
    }
  }

  const handleReviewSubmission = async (status: 'approved' | 'rejected', reason?: string, notes?: string) => {
    if (!selectedSubmission || !user) return

    try {
      setReviewLoading(true)
      await KYCService.reviewKYCSubmission(
        selectedSubmission.id,
        { status, rejection_reason: reason, admin_notes: notes },
        user.id
      )

      // Refresh submissions
      await fetchSubmissions()
      setShowReviewModal(false)
      setSelectedSubmission(null)
      setDocumentUrls({})
    } catch (error) {
      console.error('Error reviewing submission:', error)
    } finally {
      setReviewLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'under_review':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'approved':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'rejected':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const totalPages = Math.ceil(totalSubmissions / itemsPerPage)

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-amber-100 rounded-lg">
                <Shield className="h-6 w-6 text-amber-600" />
              </div>
              KYC Management
            </h1>
            <p className="text-gray-600 mt-1">Review and manage user identity verification submissions</p>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="bg-white px-4 py-2 rounded-lg border border-gray-200">
              <span className="text-sm text-gray-600">Total Submissions: </span>
              <span className="font-semibold text-gray-900">{totalSubmissions}</span>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name, email, or ID number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="under_review">Under Review</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </div>

        {/* Submissions List */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-amber-600" />
            </div>
          ) : submissions.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No KYC Submissions</h3>
              <p className="text-gray-600">No submissions found matching your criteria.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {submissions.map((submission) => (
                <motion.div
                  key={submission.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-6 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-gray-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{submission.full_name}</h3>
                            <p className="text-sm text-gray-600">{submission.user.email}</p>
                          </div>
                        </div>
                        
                        <KYCStatusBadge status={submission.status as any} />
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4" />
                          <span>{submission.id_document_type.replace('_', ' ').toUpperCase()}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(submission.created_at).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <span className="truncate">{submission.address}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setExpandedSubmission(
                          expandedSubmission === submission.id ? null : submission.id
                        )}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        {expandedSubmission === submission.id ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </button>
                      
                      <button
                        onClick={() => handleViewDocuments(submission)}
                        className="flex items-center gap-2 px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                        Review
                      </button>
                    </div>
                  </div>

                  {/* Expanded Details */}
                  <AnimatePresence>
                    {expandedSubmission === submission.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-4 pt-4 border-t border-gray-200"
                      >
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">ID Number:</span>
                            <span className="ml-2 text-gray-600">{submission.id_document_number || 'Not provided'}</span>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Date of Birth:</span>
                            <span className="ml-2 text-gray-600">
                              {submission.date_of_birth ? new Date(submission.date_of_birth).toLocaleDateString() : 'Not provided'}
                            </span>
                          </div>
                          {submission.submission_notes && (
                            <div className="md:col-span-2">
                              <span className="font-medium text-gray-700">Notes:</span>
                              <p className="mt-1 text-gray-600">{submission.submission_notes}</p>
                            </div>
                          )}
                          {submission.rejection_reason && (
                            <div className="md:col-span-2">
                              <span className="font-medium text-red-700">Rejection Reason:</span>
                              <p className="mt-1 text-red-600">{submission.rejection_reason}</p>
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between bg-white px-6 py-3 rounded-lg border border-gray-200">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalSubmissions)} of {totalSubmissions} results
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-1 text-sm bg-amber-100 text-amber-800 rounded">
                {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {/* Review Modal */}
        <AnimatePresence>
          {showReviewModal && selectedSubmission && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
              onClick={() => {
                setShowReviewModal(false)
                setSelectedSubmission(null)
                setDocumentUrls({})
              }}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h2 className="text-xl font-semibold text-gray-900">
                        Review KYC Submission
                      </h2>
                      <p className="text-gray-600 mt-1">
                        {selectedSubmission?.full_name} - {selectedSubmission?.user?.email}
                      </p>
                    </div>
                    <button
                      onClick={() => {
                        setShowReviewModal(false)
                        setSelectedSubmission(null)
                        setDocumentUrls({})
                      }}
                      className="p-2 text-gray-400 hover:text-gray-600"
                    >
                      <XCircle className="h-6 w-6" />
                    </button>
                  </div>
                </div>

                <div className="p-6 space-y-6">
                  {/* Submission Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <p className="text-gray-900">{selectedSubmission?.full_name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ID Document Type
                      </label>
                      <p className="text-gray-900">{selectedSubmission?.id_document_type?.replace('_', ' ').toUpperCase()}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        ID Number
                      </label>
                      <p className="text-gray-900">{selectedSubmission?.id_document_number || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date of Birth
                      </label>
                      <p className="text-gray-900">
                        {selectedSubmission?.date_of_birth
                          ? new Date(selectedSubmission.date_of_birth).toLocaleDateString()
                          : 'Not provided'
                        }
                      </p>
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Address
                      </label>
                      <p className="text-gray-900">{selectedSubmission?.address}</p>
                    </div>
                    {selectedSubmission?.submission_notes && (
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          User Notes
                        </label>
                        <p className="text-gray-900">{selectedSubmission?.submission_notes}</p>
                      </div>
                    )}
                  </div>

                  {/* Documents */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Submitted Documents</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(documentUrls).map(([key, url]) => {
                        const docType = key === 'id_front' ? 'ID Front' :
                                       key === 'id_back' ? 'ID Back' :
                                       key === 'selfie' ? 'Selfie' :
                                       key === 'address_proof' ? 'Address Proof' : 'Document'
                        return (
                          <div key={key} className="border border-gray-200 rounded-lg p-4">
                            <h4 className="font-medium text-gray-900 mb-2">{docType}</h4>
                            <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                              {url ? (
                                <img
                                  src={url}
                                  alt={docType}
                                  className="w-full h-full object-contain"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement
                                    target.style.display = 'none'
                                    const parent = target.parentElement
                                    if (parent) {
                                      parent.innerHTML = `
                                        <div class="flex items-center justify-center h-full">
                                          <div class="text-center">
                                            <div class="h-8 w-8 text-gray-400 mx-auto mb-2">⚠️</div>
                                            <p class="text-sm text-gray-600">Failed to load document</p>
                                          </div>
                                        </div>
                                      `
                                    }
                                  }}
                                />
                              ) : (
                                <div className="flex items-center justify-center h-full">
                                  <div className="text-center">
                                    <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                                    <p className="text-sm text-gray-600">No document available</p>
                                  </div>
                                </div>
                              )}
                            </div>
                            <a
                              href={url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center gap-2 mt-2 text-sm text-amber-600 hover:text-amber-700"
                            >
                              <Download className="h-4 w-4" />
                              View Full Size
                            </a>
                          </div>
                        )
                      })}
                    </div>
                  </div>

                  {/* Review Actions */}
                  <div className="flex items-center justify-end gap-3 pt-6 border-t border-gray-200">
                    <button
                      onClick={() => handleReviewSubmission('rejected', 'Documents do not meet requirements')}
                      disabled={reviewLoading}
                      className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors"
                    >
                      {reviewLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <XCircle className="h-4 w-4" />
                      )}
                      Reject
                    </button>
                    <button
                      onClick={() => handleReviewSubmission('approved')}
                      disabled={reviewLoading}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors"
                    >
                      {reviewLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <CheckCircle className="h-4 w-4" />
                      )}
                      Approve
                    </button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </AdminLayout>
  )
}
