import { supabase } from '@/lib/supabase'

export type KYCDocumentType = 'id_front' | 'id_back' | 'selfie' | 'address_proof'

export interface KYCDocumentUpload {
  file: File
  type: KYCDocumentType
  description?: string
}

export class KYCStorageService {
  private static readonly BUCKET_NAME = 'kyc-documents'
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents
  private static readonly ALLOWED_TYPES = [
    'image/jpeg', 
    'image/jpg', 
    'image/png', 
    'image/webp',
    'application/pdf' // Allow PDF for address proof documents
  ]

  /**
   * Ensure KYC bucket exists before operations
   */
  private static async ensureBucketExists(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets()

      if (listError) {
        console.warn('Cannot list buckets (likely due to RLS policies):', listError.message)
        console.log('Assuming KYC bucket exists and continuing...')
        return
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME)

      if (!bucketExists) {
        console.log(`KYC bucket '${this.BUCKET_NAME}' not found in bucket list`)
        console.log('This is expected if RLS policies restrict bucket listing')
        console.log('Assuming bucket exists and continuing with upload...')
        return
      }

      console.log(`KYC bucket '${this.BUCKET_NAME}' confirmed to exist`)
    } catch (error) {
      console.warn('Error checking KYC bucket existence:', error)
      console.log('Assuming bucket exists and continuing with operations...')
    }
  }

  /**
   * Validate KYC document file
   */
  private static validateFile(file: File, documentType: KYCDocumentType): void {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`)
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      throw new Error(`File type ${file.type} is not supported. Allowed types: ${this.ALLOWED_TYPES.join(', ')}`)
    }

    // Additional validation for specific document types
    if (documentType === 'selfie' && file.type === 'application/pdf') {
      throw new Error('Selfie photos must be image files, not PDF')
    }

    // Check if file name is reasonable
    if (file.name.length > 255) {
      throw new Error('File name is too long')
    }
  }

  /**
   * Generate secure filename for KYC document
   */
  private static generateSecureFileName(userId: string, documentType: KYCDocumentType, originalName: string): string {
    const timestamp = Date.now()
    const randomId = Math.random().toString(36).substring(2)
    const fileExt = originalName.split('.').pop()?.toLowerCase() || 'jpg'
    
    // Create folder structure: userId/documentType/timestamp-randomId.ext
    return `${userId}/${documentType}/${timestamp}-${randomId}.${fileExt}`
  }

  /**
   * Upload a single KYC document
   */
  static async uploadKYCDocument(
    file: File,
    userId: string,
    documentType: KYCDocumentType
  ): Promise<string> {
    try {
      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user) {
        console.error('Authentication error during KYC upload:', authError)
        throw new Error('Authentication required. Please log in and try again.')
      }

      if (user.id !== userId) {
        console.error('User ID mismatch during KYC upload:', { authUserId: user.id, providedUserId: userId })
        throw new Error('Authentication error. Please log out and log in again.')
      }

      console.log(`Authenticated user ${user.id} uploading KYC document: ${documentType}`)

      // Ensure bucket exists (or assume it exists)
      await this.ensureBucketExists()

      // Validate file
      this.validateFile(file, documentType)

      // Generate secure filename
      const fileName = this.generateSecureFileName(userId, documentType, file.name)

      console.log(`Uploading KYC document: ${fileName} to bucket: ${this.BUCKET_NAME}`)

      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true // Allow overwriting for resubmissions
        })

      if (error) {
        console.error('KYC document upload error:', {
          error,
          fileName,
          fileSize: file.size,
          fileType: file.type,
          userId,
          documentType
        })

        // Provide more specific error messages
        if (error.message.includes('row-level security') || error.message.includes('new row violates row-level security')) {
          throw new Error('Upload failed: Storage access denied. Please ensure you are logged in and try again.')
        } else if (error.message.includes('Bucket not found')) {
          throw new Error('Upload failed: Storage bucket not configured. Please contact support.')
        } else if (error.message.includes('File size') || error.message.includes('too large')) {
          throw new Error(`Upload failed: File too large. Maximum size is ${this.MAX_FILE_SIZE / (1024 * 1024)}MB.`)
        } else if (error.message.includes('Invalid file type')) {
          throw new Error(`Upload failed: Invalid file type. Allowed types: ${this.ALLOWED_TYPES.join(', ')}`)
        } else {
          throw new Error(`Upload failed: ${error.message}`)
        }
      }

      console.log('KYC document upload successful:', {
        fileName,
        path: data?.path,
        fullPath: data?.fullPath
      })

      // For private buckets, we need to create signed URLs for access
      // Return the file path for now, signed URL will be generated when needed
      return fileName
    } catch (error) {
      console.error('Error uploading KYC document:', error)
      throw error
    }
  }

  /**
   * Upload multiple KYC documents
   */
  static async uploadKYCDocuments(
    documents: KYCDocumentUpload[], 
    userId: string
  ): Promise<Record<KYCDocumentType, string>> {
    const results: Record<string, string> = {}

    try {
      // Upload all documents concurrently
      const uploadPromises = documents.map(async (doc) => {
        const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type)
        return { type: doc.type, filePath }
      })

      const uploadResults = await Promise.all(uploadPromises)

      // Build results object
      uploadResults.forEach(result => {
        results[result.type] = result.filePath
      })

      return results as Record<KYCDocumentType, string>
    } catch (error) {
      console.error('Error uploading multiple KYC documents:', error)
      throw error
    }
  }

  /**
   * Get signed URL for KYC document (for admin review)
   */
  static async getKYCDocumentSignedUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    try {
      console.log(`Creating signed URL for file: ${filePath} in bucket: ${this.BUCKET_NAME}`)

      // Check current user authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser()
      if (authError) {
        console.error('Authentication error:', authError)
      } else {
        console.log('Current user:', user?.email, 'ID:', user?.id)
      }

      // Check if file exists first
      const pathParts = filePath.split('/')
      const directory = pathParts.slice(0, -1).join('/')
      const fileName = pathParts[pathParts.length - 1]

      console.log(`Checking if file exists in directory: ${directory}`)
      const { data: files, error: listError } = await supabase.storage
        .from(this.BUCKET_NAME)
        .list(directory)

      if (listError) {
        console.error('Error listing directory:', listError)
      } else {
        console.log(`Files in directory ${directory}:`, files?.map(f => f.name))
        const fileExists = files?.some(f => f.name === fileName)
        console.log(`File ${fileName} exists:`, fileExists)
      }

      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn)

      if (error) {
        console.error('Error creating signed URL:', {
          error,
          filePath,
          bucketName: this.BUCKET_NAME,
          expiresIn,
          errorMessage: error.message,
          errorStatus: error.statusCode
        })
        throw new Error(`Failed to create signed URL for ${filePath}: ${error.message}`)
      }

      if (!data?.signedUrl) {
        throw new Error(`No signed URL returned for ${filePath}`)
      }

      console.log(`Successfully created signed URL for: ${filePath}`)
      return data.signedUrl
    } catch (error) {
      console.error('Error getting KYC document signed URL:', {
        error,
        filePath,
        bucketName: this.BUCKET_NAME
      })
      throw error
    }
  }

  /**
   * Delete KYC document (for cleanup or resubmission)
   */
  static async deleteKYCDocument(filePath: string): Promise<void> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath])

      if (error) {
        console.error('Error deleting KYC document:', error)
        throw new Error(`Failed to delete document: ${error.message}`)
      }

      console.log('KYC document deleted successfully:', filePath)
    } catch (error) {
      console.error('Error deleting KYC document:', error)
      throw error
    }
  }

  /**
   * Get multiple signed URLs for KYC documents
   */
  static async getKYCDocumentSignedUrls(
    filePaths: string[],
    expiresIn: number = 3600
  ): Promise<Record<string, string>> {
    try {
      console.log(`Creating signed URLs for ${filePaths.length} files:`, filePaths)

      const urlPromises = filePaths.map(async (filePath) => {
        try {
          const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn)
          return { filePath, signedUrl, success: true }
        } catch (error) {
          console.error(`Failed to create signed URL for ${filePath}:`, error)
          return { filePath, signedUrl: '', success: false, error }
        }
      })

      const results = await Promise.all(urlPromises)

      const urlMap: Record<string, string> = {}
      const failedFiles: string[] = []

      results.forEach(result => {
        if (result.success && result.signedUrl) {
          urlMap[result.filePath] = result.signedUrl
        } else {
          failedFiles.push(result.filePath)
        }
      })

      if (failedFiles.length > 0) {
        console.warn(`Failed to create signed URLs for files:`, failedFiles)
      }

      console.log(`Successfully created ${Object.keys(urlMap).length} signed URLs out of ${filePaths.length} requested`)
      return urlMap
    } catch (error) {
      console.error('Error getting multiple KYC document signed URLs:', error)
      throw error
    }
  }

  /**
   * Validate document type enum
   */
  static isValidDocumentType(type: string): type is KYCDocumentType {
    return ['id_front', 'id_back', 'selfie', 'address_proof'].includes(type)
  }

  /**
   * Get human-readable document type name
   */
  static getDocumentTypeName(type: KYCDocumentType): string {
    const names: Record<KYCDocumentType, string> = {
      id_front: 'ID Document (Front)',
      id_back: 'ID Document (Back)',
      selfie: 'Selfie Photo',
      address_proof: 'Address Proof'
    }
    return names[type]
  }

  /**
   * Get document type requirements
   */
  static getDocumentTypeRequirements(type: KYCDocumentType): string {
    const requirements: Record<KYCDocumentType, string> = {
      id_front: 'Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)',
      id_back: 'Clear photo of the back side of your government-issued ID',
      selfie: 'Clear selfie photo holding your ID document next to your face',
      address_proof: 'Recent utility bill, bank statement, or official document showing your address (PDF or image)'
    }
    return requirements[type]
  }
}
