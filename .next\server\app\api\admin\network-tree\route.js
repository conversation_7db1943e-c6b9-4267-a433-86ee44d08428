"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/network-tree/route";
exports.ids = ["app/api/admin/network-tree/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_network_tree_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/network-tree/route.ts */ \"(rsc)/./src/app/api/admin/network-tree/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/network-tree/route\",\n        pathname: \"/api/admin/network-tree\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/network-tree/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\network-tree\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_network_tree_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/network-tree/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/network-tree/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/admin/network-tree/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(rsc)/./src/lib/services/referralSystem.ts\");\n\n\n\n\n\nasync function GET(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin using admin client\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const rootUserId = searchParams.get(\"rootUserId\");\n        const maxDepth = parseInt(searchParams.get(\"maxDepth\") || \"10\");\n        let networkTree;\n        if (rootUserId) {\n            // Get network tree for specific user\n            networkTree = await getNetworkTreeForUser(rootUserId, maxDepth);\n        } else {\n            // Get complete network tree starting from OKDOI Head\n            const okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_4__.ReferralSystemService.getOKDOIHead();\n            if (!okdoiHead) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: null,\n                    message: \"No OKDOI Head found\"\n                });\n            }\n            networkTree = await getNetworkTreeForUser(okdoiHead.id, maxDepth);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: networkTree\n        });\n    } catch (error) {\n        console.error(\"Error fetching network tree:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch network tree\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function getNetworkTreeForUser(userId, maxDepth = 10) {\n    try {\n        // Get the root user\n        const { data: rootUser, error: rootError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n        if (rootError || !rootUser) {\n            throw new Error(\"Root user not found\");\n        }\n        // Build the tree recursively\n        const buildTree = async (user, currentDepth)=>{\n            const children = [];\n            if (currentDepth < maxDepth) {\n                // Get direct referrals\n                const { data: referrals, error: referralsError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"*\").eq(\"referred_by_id\", user.id).order(\"created_at\");\n                if (!referralsError && referrals) {\n                    for (const referral of referrals){\n                        const childNode = await buildTree(referral, currentDepth + 1);\n                        children.push(childNode);\n                    }\n                }\n            }\n            // Get user stats\n            const stats = await getUserStats(user.id);\n            return {\n                user,\n                children,\n                level: currentDepth,\n                position: 0,\n                stats\n            };\n        };\n        return await buildTree(rootUser, 0);\n    } catch (error) {\n        console.error(\"Error building network tree:\", error);\n        throw error;\n    }\n}\nasync function getUserStats(userId) {\n    try {\n        // Get direct referrals count\n        const { count: directReferrals } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"id\", {\n            count: \"exact\"\n        }).eq(\"referred_by_id\", userId);\n        // Get total downline count (recursive)\n        const totalDownline = await getTotalDownlineCount(userId);\n        // Get total commission earned\n        const { data: commissionData } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"commission_transactions\").select(\"amount\").eq(\"recipient_id\", userId).eq(\"status\", \"completed\");\n        const totalCommission = commissionData?.reduce((sum, transaction)=>sum + transaction.amount, 0) || 0;\n        return {\n            directReferrals: directReferrals || 0,\n            totalDownline: totalDownline || 0,\n            totalCommission: totalCommission || 0\n        };\n    } catch (error) {\n        console.error(\"Error getting user stats:\", error);\n        return {\n            directReferrals: 0,\n            totalDownline: 0,\n            totalCommission: 0\n        };\n    }\n}\nasync function getTotalDownlineCount(userId) {\n    try {\n        // Get direct referrals\n        const { data: directReferrals } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabaseAdmin.from(\"users\").select(\"id\").eq(\"referred_by_id\", userId);\n        if (!directReferrals || directReferrals.length === 0) {\n            return 0;\n        }\n        let totalCount = directReferrals.length;\n        // Recursively count downline for each direct referral\n        for (const referral of directReferrals){\n            const downlineCount = await getTotalDownlineCount(referral.id);\n            totalCount += downlineCount;\n        }\n        return totalCount;\n    } catch (error) {\n        console.error(\"Error getting total downline count:\", error);\n        return 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/network-tree/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/referralSystem.ts":
/*!********************************************!*\
  !*** ./src/lib/services/referralSystem.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReferralSystemService: () => (/* binding */ ReferralSystemService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * ReferralSystemService - Manages the multi-level referral and commission system\n */ class ReferralSystemService {\n    /**\n   * Generate a unique referral code\n   */ static async generateReferralCode() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"generate_referral_code\");\n        if (error) {\n            throw new Error(`Failed to generate referral code: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Validate referral code and get referrer information\n   */ static async validateReferralCode(code) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"referral_code\", code).eq(\"is_referral_active\", true).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Code not found\n                    ;\n                }\n                throw new Error(`Failed to validate referral code: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error validating referral code:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Place user in referral hierarchy\n   */ static async placeUserInHierarchy(newUserId, referrerId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"place_user_in_hierarchy\", {\n                new_user_id: newUserId,\n                referrer_id: referrerId\n            });\n            if (error) {\n                throw new Error(`Failed to place user in hierarchy: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error placing user in hierarchy:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral statistics\n   */ static async getUserReferralStats(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"direct_referrals_count, total_downline_count, total_commission_earned, referral_level, referral_code\").eq(\"id\", userId).single();\n            if (error) {\n                throw new Error(`Failed to get referral stats: ${error.message}`);\n            }\n            let referralCode = data.referral_code;\n            // If user doesn't have a referral code, generate one\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n                // Update the user with the new referral code\n                const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                    referral_code: referralCode\n                }).eq(\"id\", userId);\n                if (updateError) {\n                    console.error(\"Error updating referral code:\", updateError);\n                // Don't throw error, just use the generated code\n                }\n            }\n            return {\n                directReferrals: data.direct_referrals_count || 0,\n                totalDownline: data.total_downline_count || 0,\n                totalCommissionEarned: data.total_commission_earned || 0,\n                currentLevel: data.referral_level || 0,\n                totalReferrals: data.direct_referrals_count || 0,\n                referralCode: referralCode\n            };\n        } catch (error) {\n            console.error(\"Error getting referral stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's direct referrals\n   */ static async getDirectReferrals(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n          child_id,\n          position,\n          created_at,\n          child:child_id(*)\n        `).eq(\"parent_id\", userId).order(\"position\");\n            if (error) {\n                throw new Error(`Failed to get direct referrals: ${error.message}`);\n            }\n            return data?.map((item)=>item.child) || [];\n        } catch (error) {\n            console.error(\"Error getting direct referrals:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral tree (up to specified depth)\n   */ static async getReferralTree(userId, maxDepth = 3) {\n        try {\n            // Get the root user\n            const { data: rootUser, error: rootError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (rootError) {\n                throw new Error(`Failed to get root user: ${rootError.message}`);\n            }\n            // Build the tree recursively\n            const buildTree = async (user, currentDepth)=>{\n                const children = [];\n                if (currentDepth < maxDepth) {\n                    const { data: placements, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n              position,\n              child:child_id(*)\n            `).eq(\"parent_id\", user.id).order(\"position\");\n                    if (!error && placements) {\n                        for (const placement of placements){\n                            const childNode = await buildTree(placement.child, currentDepth + 1);\n                            childNode.position = placement.position;\n                            children.push(childNode);\n                        }\n                    }\n                }\n                return {\n                    user,\n                    children,\n                    level: currentDepth,\n                    position: 0 // Will be set by parent\n                };\n            };\n            return await buildTree(rootUser, 0);\n        } catch (error) {\n            console.error(\"Error getting referral tree:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's commission transactions\n   */ static async getCommissionTransactions(userId, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            const [dataResult, countResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId).order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"beneficiary_id\", userId)\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create Zonal Manager\n   */ static async createZonalManager(userId, zoneName, zoneDescription, assignedDistricts = [], createdBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"zonal_manager\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create zonal manager record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).insert({\n                user_id: userId,\n                zone_name: zoneName,\n                zone_description: zoneDescription,\n                assigned_districts: assignedDistricts,\n                created_by: createdBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create zonal manager: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upgrade user to RSM\n   */ static async upgradeToRSM(userId, zonalManagerId, regionName, upgradedBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"rsm\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create RSM record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).insert({\n                user_id: userId,\n                zonal_manager_id: zonalManagerId,\n                region_name: regionName,\n                upgraded_by: upgradedBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create RSM: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error upgrading to RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Zonal Managers\n   */ static async getZonalManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(`\n          *,\n          user:user_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get zonal managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting zonal managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Regional Sales Managers\n   */ static async getRegionalSalesManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          *,\n          user:user_id(*),\n          zonal_manager:zonal_manager_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get regional sales managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting regional sales managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Search users for ZM/RSM creation\n   */ static async searchUsers(searchTerm, limit = 20) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`).eq(\"user_type\", \"user\") // Only regular users can be upgraded\n            .limit(limit);\n            if (error) {\n                throw new Error(`Failed to search users: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get referral system statistics\n   */ static async getReferralSystemStats() {\n        try {\n            const [totalUsersResult, zonalManagersResult, regionalManagersResult, totalReferralsResult, activeCodesResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).not(\"referral_code\", \"is\", null)\n            ]);\n            return {\n                totalUsers: totalUsersResult.count || 0,\n                zonalManagers: zonalManagersResult.count || 0,\n                regionalManagers: regionalManagersResult.count || 0,\n                totalReferrals: totalReferralsResult.count || 0,\n                activeReferralCodes: activeCodesResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting referral system stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Zonal Manager\n   */ static async deactivateZonalManager(zmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", zmId);\n            if (error) {\n                throw new Error(`Failed to deactivate zonal manager: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Regional Sales Manager\n   */ static async deactivateRSM(rsmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", rsmId);\n            if (error) {\n                throw new Error(`Failed to deactivate RSM: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create or get OKDOI Head user\n   */ static async createOKDOIHead(email = \"<EMAIL>\", fullName = \"OKDOI Head\", phone) {\n        try {\n            // Check if OKDOI Head already exists using admin client\n            const { data: existingHead, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (existingHead && !checkError) {\n                return existingHead;\n            }\n            // Generate referral code first\n            const referralCode = await this.generateReferralCode();\n            // Create auth user first using admin auth API\n            const { data: authUser, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.createUser({\n                email,\n                password: crypto.randomUUID(),\n                email_confirm: true,\n                user_metadata: {\n                    full_name: fullName,\n                    phone: phone || null\n                }\n            });\n            if (authError || !authUser.user) {\n                throw new Error(`Failed to create auth user: ${authError?.message}`);\n            }\n            // Create OKDOI Head user in public.users table using the auth user ID\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).insert({\n                id: authUser.user.id,\n                email,\n                full_name: fullName,\n                phone,\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                // If user creation fails, clean up the auth user\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(authUser.user.id);\n                throw new Error(`Failed to create OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Assign OKDOI Head role to existing user\n   */ static async assignOKDOIHeadToUser(userId) {\n        try {\n            // Check if OKDOI Head already exists\n            const existingHead = await this.getOKDOIHead();\n            if (existingHead) {\n                throw new Error(\"OKDOI Head already exists. Only one OKDOI Head is allowed.\");\n            }\n            // Get the user to be assigned\n            const { data: user, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (userError || !user) {\n                throw new Error(\"User not found\");\n            }\n            // Generate referral code if user doesn't have one\n            let referralCode = user.referral_code;\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n            }\n            // Update user to OKDOI Head\n            const { data: updatedUser, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", userId).select().single();\n            if (updateError) {\n                throw new Error(`Failed to assign OKDOI Head: ${updateError.message}`);\n            }\n            return updatedUser;\n        } catch (error) {\n            console.error(\"Error assigning OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get OKDOI Head user\n   */ static async getOKDOIHead() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // No OKDOI Head found\n                    ;\n                }\n                throw new Error(`Failed to get OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting OKDOI Head:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/referralSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&page=%2Fapi%2Fadmin%2Fnetwork-tree%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fnetwork-tree%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();