"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/lib/services/kycStorage.ts":
/*!****************************************!*\
  !*** ./src/lib/services/kycStorage.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KYCStorageService: function() { return /* binding */ KYCStorageService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\nclass KYCStorageService {\n    /**\n   * Ensure KYC bucket exists before operations\n   */ static async ensureBucketExists() {\n        try {\n            // Check if bucket exists\n            const { data: buckets, error: listError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.listBuckets();\n            if (listError) {\n                console.warn(\"Cannot list buckets (likely due to RLS policies):\", listError.message);\n                console.log(\"Assuming KYC bucket exists and continuing...\");\n                return;\n            }\n            const bucketExists = buckets === null || buckets === void 0 ? void 0 : buckets.some((bucket)=>bucket.name === this.BUCKET_NAME);\n            if (!bucketExists) {\n                console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' not found in bucket list\"));\n                console.log(\"This is expected if RLS policies restrict bucket listing\");\n                console.log(\"Assuming bucket exists and continuing with upload...\");\n                return;\n            }\n            console.log(\"KYC bucket '\".concat(this.BUCKET_NAME, \"' confirmed to exist\"));\n        } catch (error) {\n            console.warn(\"Error checking KYC bucket existence:\", error);\n            console.log(\"Assuming bucket exists and continuing with operations...\");\n        }\n    }\n    /**\n   * Validate KYC document file\n   */ static validateFile(file, documentType) {\n        // Check file size\n        if (file.size > this.MAX_FILE_SIZE) {\n            throw new Error(\"File size must be less than \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB\"));\n        }\n        // Check file type\n        if (!this.ALLOWED_TYPES.includes(file.type)) {\n            throw new Error(\"File type \".concat(file.type, \" is not supported. Allowed types: \").concat(this.ALLOWED_TYPES.join(\", \")));\n        }\n        // Additional validation for specific document types\n        if (documentType === \"selfie\" && file.type === \"application/pdf\") {\n            throw new Error(\"Selfie photos must be image files, not PDF\");\n        }\n        // Check if file name is reasonable\n        if (file.name.length > 255) {\n            throw new Error(\"File name is too long\");\n        }\n    }\n    /**\n   * Generate secure filename for KYC document\n   */ static generateSecureFileName(userId, documentType, originalName) {\n        var _originalName_split_pop;\n        const timestamp = Date.now();\n        const randomId = Math.random().toString(36).substring(2);\n        const fileExt = ((_originalName_split_pop = originalName.split(\".\").pop()) === null || _originalName_split_pop === void 0 ? void 0 : _originalName_split_pop.toLowerCase()) || \"jpg\";\n        // Create folder structure: userId/documentType/timestamp-randomId.ext\n        return \"\".concat(userId, \"/\").concat(documentType, \"/\").concat(timestamp, \"-\").concat(randomId, \".\").concat(fileExt);\n    }\n    /**\n   * Upload a single KYC document\n   */ static async uploadKYCDocument(file, userId, documentType) {\n        try {\n            // Check authentication first\n            const { data: { user }, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n            if (authError || !user) {\n                console.error(\"Authentication error during KYC upload:\", authError);\n                throw new Error(\"Authentication required. Please log in and try again.\");\n            }\n            if (user.id !== userId) {\n                console.error(\"User ID mismatch during KYC upload:\", {\n                    authUserId: user.id,\n                    providedUserId: userId\n                });\n                throw new Error(\"Authentication error. Please log out and log in again.\");\n            }\n            console.log(\"Authenticated user \".concat(user.id, \" uploading KYC document: \").concat(documentType));\n            // Ensure bucket exists (or assume it exists)\n            await this.ensureBucketExists();\n            // Validate file\n            this.validateFile(file, documentType);\n            // Generate secure filename\n            const fileName = this.generateSecureFileName(userId, documentType, file.name);\n            console.log(\"Uploading KYC document: \".concat(fileName, \" to bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).upload(fileName, file, {\n                cacheControl: \"3600\",\n                upsert: true // Allow overwriting for resubmissions\n            });\n            if (error) {\n                console.error(\"KYC document upload error:\", {\n                    error,\n                    fileName,\n                    fileSize: file.size,\n                    fileType: file.type,\n                    userId,\n                    documentType\n                });\n                // Provide more specific error messages\n                if (error.message.includes(\"row-level security\") || error.message.includes(\"new row violates row-level security\")) {\n                    throw new Error(\"Upload failed: Storage access denied. Please ensure you are logged in and try again.\");\n                } else if (error.message.includes(\"Bucket not found\")) {\n                    throw new Error(\"Upload failed: Storage bucket not configured. Please contact support.\");\n                } else if (error.message.includes(\"File size\") || error.message.includes(\"too large\")) {\n                    throw new Error(\"Upload failed: File too large. Maximum size is \".concat(this.MAX_FILE_SIZE / (1024 * 1024), \"MB.\"));\n                } else if (error.message.includes(\"Invalid file type\")) {\n                    throw new Error(\"Upload failed: Invalid file type. Allowed types: \".concat(this.ALLOWED_TYPES.join(\", \")));\n                } else {\n                    throw new Error(\"Upload failed: \".concat(error.message));\n                }\n            }\n            console.log(\"KYC document upload successful:\", {\n                fileName,\n                path: data === null || data === void 0 ? void 0 : data.path,\n                fullPath: data === null || data === void 0 ? void 0 : data.fullPath\n            });\n            // For private buckets, we need to create signed URLs for access\n            // Return the file path for now, signed URL will be generated when needed\n            return fileName;\n        } catch (error) {\n            console.error(\"Error uploading KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upload multiple KYC documents\n   */ static async uploadKYCDocuments(documents, userId) {\n        const results = {};\n        try {\n            // Upload all documents concurrently\n            const uploadPromises = documents.map(async (doc)=>{\n                const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type);\n                return {\n                    type: doc.type,\n                    filePath\n                };\n            });\n            const uploadResults = await Promise.all(uploadPromises);\n            // Build results object\n            uploadResults.forEach((result)=>{\n                results[result.type] = result.filePath;\n            });\n            return results;\n        } catch (error) {\n            console.error(\"Error uploading multiple KYC documents:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get signed URL for KYC document (for admin review)\n   */ static async getKYCDocumentSignedUrl(filePath) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            console.log(\"Creating signed URL for file: \".concat(filePath, \" in bucket: \").concat(this.BUCKET_NAME));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).createSignedUrl(filePath, expiresIn);\n            if (error) {\n                console.error(\"Error creating signed URL:\", {\n                    error,\n                    filePath,\n                    bucketName: this.BUCKET_NAME,\n                    expiresIn\n                });\n                throw new Error(\"Failed to create signed URL for \".concat(filePath, \": \").concat(error.message));\n            }\n            if (!(data === null || data === void 0 ? void 0 : data.signedUrl)) {\n                throw new Error(\"No signed URL returned for \".concat(filePath));\n            }\n            console.log(\"Successfully created signed URL for: \".concat(filePath));\n            return data.signedUrl;\n        } catch (error) {\n            console.error(\"Error getting KYC document signed URL:\", {\n                error,\n                filePath,\n                bucketName: this.BUCKET_NAME\n            });\n            throw error;\n        }\n    }\n    /**\n   * Delete KYC document (for cleanup or resubmission)\n   */ static async deleteKYCDocument(filePath) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.storage.from(this.BUCKET_NAME).remove([\n                filePath\n            ]);\n            if (error) {\n                console.error(\"Error deleting KYC document:\", error);\n                throw new Error(\"Failed to delete document: \".concat(error.message));\n            }\n            console.log(\"KYC document deleted successfully:\", filePath);\n        } catch (error) {\n            console.error(\"Error deleting KYC document:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get multiple signed URLs for KYC documents\n   */ static async getKYCDocumentSignedUrls(filePaths) {\n        let expiresIn = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3600;\n        try {\n            const urlPromises = filePaths.map(async (filePath)=>{\n                const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn);\n                return {\n                    filePath,\n                    signedUrl\n                };\n            });\n            const results = await Promise.all(urlPromises);\n            const urlMap = {};\n            results.forEach((result)=>{\n                urlMap[result.filePath] = result.signedUrl;\n            });\n            return urlMap;\n        } catch (error) {\n            console.error(\"Error getting multiple KYC document signed URLs:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Validate document type enum\n   */ static isValidDocumentType(type) {\n        return [\n            \"id_front\",\n            \"id_back\",\n            \"selfie\",\n            \"address_proof\"\n        ].includes(type);\n    }\n    /**\n   * Get human-readable document type name\n   */ static getDocumentTypeName(type) {\n        const names = {\n            id_front: \"ID Document (Front)\",\n            id_back: \"ID Document (Back)\",\n            selfie: \"Selfie Photo\",\n            address_proof: \"Address Proof\"\n        };\n        return names[type];\n    }\n    /**\n   * Get document type requirements\n   */ static getDocumentTypeRequirements(type) {\n        const requirements = {\n            id_front: \"Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)\",\n            id_back: \"Clear photo of the back side of your government-issued ID\",\n            selfie: \"Clear selfie photo holding your ID document next to your face\",\n            address_proof: \"Recent utility bill, bank statement, or official document showing your address (PDF or image)\"\n        };\n        return requirements[type];\n    }\n}\nKYCStorageService.BUCKET_NAME = \"kyc-documents\";\nKYCStorageService.MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents\n;\nKYCStorageService.ALLOWED_TYPES = [\n    \"image/jpeg\",\n    \"image/jpg\",\n    \"image/png\",\n    \"image/webp\",\n    \"application/pdf\" // Allow PDF for address proof documents\n];\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/kycStorage.ts\n"));

/***/ })

});