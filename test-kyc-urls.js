// Test script to check KYC document URL generation with admin authentication
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://vnmydqbwjjufnxngpnqo.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo'

const supabase = createClient(supabaseUrl, supabaseKey)

async function signInAsAdmin() {
  try {
    console.log('Signing in as admin user...')
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>', // Admin user from the database
      password: 'password123' // You'll need to provide the correct password
    })

    if (error) {
      console.error('Sign in error:', error)
      return false
    }

    console.log('Successfully signed in as:', data.user.email)
    return true
  } catch (error) {
    console.error('Sign in failed:', error)
    return false
  }
}

async function testKYCDocumentUrls() {
  try {
    console.log('Testing KYC document URL generation...')

    // First sign in as admin
    const signedIn = await signInAsAdmin()
    if (!signedIn) {
      console.log('Skipping authenticated tests due to sign-in failure')
      return
    }

    // Check current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) {
      console.error('Error getting current user:', userError)
      return
    }
    console.log('Current authenticated user:', user?.email)

    // Check user role from database
    const { data: userData, error: roleError } = await supabase
      .from('users')
      .select('role, is_super_admin')
      .eq('id', user.id)
      .single()

    if (roleError) {
      console.error('Error getting user role:', roleError)
    } else {
      console.log('User role:', userData.role, 'Super admin:', userData.is_super_admin)
    }

    // Now test bucket access
    console.log('\nChecking bucket access...')
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets()
    if (bucketError) {
      console.error('Error listing buckets:', bucketError)
    } else {
      console.log('Available buckets:', buckets.map(b => b.name))
    }

    // Test file path from the database
    const testFilePath = '64edf41b-1047-46d3-88f6-10b210e900cc/id_front/1757234467993-ncfrt1o78pn.png'

    console.log('\nTesting file access...')
    console.log('File path:', testFilePath)

    // Try to list files in the root directory
    console.log('\nListing root directory...')
    const { data: rootFiles, error: rootError } = await supabase.storage
      .from('kyc-documents')
      .list()

    if (rootError) {
      console.error('Error listing root directory:', rootError)
    } else {
      console.log('Root directory contents:', rootFiles)
    }

    // Try to list files in the user directory
    console.log('\nListing user directory...')
    const { data: userFiles, error: userError2 } = await supabase.storage
      .from('kyc-documents')
      .list('64edf41b-1047-46d3-88f6-10b210e900cc')

    if (userError2) {
      console.error('Error listing user directory:', userError2)
    } else {
      console.log('User directory contents:', userFiles)
    }

    console.log('\nCreating signed URL for:', testFilePath)

    const { data, error } = await supabase.storage
      .from('kyc-documents')
      .createSignedUrl(testFilePath, 3600)

    if (error) {
      console.error('Error creating signed URL:', error)
    } else {
      console.log('Successfully created signed URL:', data.signedUrl)
    }

  } catch (error) {
    console.error('Test failed:', error)
  }
}

// Run the test
async function runTest() {
  await testKYCDocumentUrls()
  process.exit(0)
}

runTest()
